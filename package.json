{"name": "mcp-figma-to-react", "version": "1.0.0", "description": "MCP server for converting Figma designs to React components", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc -w & node --watch dist/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["figma", "react", "mcp", "component-generator"], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "1.10.2", "axios": "^1.8.4", "express": "5.1.0", "prettier": "^3.5.3", "typescript": "5.8.3", "ws": "^8.18.1", "zod": "3.24.3"}, "devDependencies": {"@types/express": "^5.0.1", "@types/node": "22.14.1", "@types/ws": "8.18.1"}}